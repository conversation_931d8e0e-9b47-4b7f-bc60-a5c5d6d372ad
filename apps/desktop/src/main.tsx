import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import { invoke } from "@tauri-apps/api/core";

import "@xyflow/react/dist/base.css";
// import "@xyflow/react/dist/style.css";
import "./styles/index.css";

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<App />
	</StrictMode>,
);

// Utility function to implement a sleep function in TypeScript
function sleep(seconds: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, seconds * 1000));
}

// Setup function
async function setup() {
	// Fake perform some really heavy setup task
	console.log("Performing really heavy frontend setup task...");
	await sleep(3);
	console.log("Frontend setup task complete!");
	// Set the frontend task as being completed
	invoke("set_complete", { task: "frontend" });
}

// Effectively a JavaScript main function
window.addEventListener("DOMContentLoaded", () => {
	setup();
});

import { listen } from "@tauri-apps/api/event";
import { usePipelineStore } from "@stores/pipeline.store.ts";
import { useUIStore } from "@stores/ui.store.ts";

listen<string>("helios/rust-event", (event) => {
	if (event.payload === "app-settings") {
		const openSettingsDialog = useUIStore.getState().openSettingsDialog;
		openSettingsDialog();
		console.log("Open settings");
	}
});

listen<string>("helios/pipeline-file-loaded", (event) => {
	const setFileName = usePipelineStore.getState().setPipelineFile;

	setFileName(event.payload);
	console.log("Load file: ", event.payload);
});

// import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
// const appWebview = getCurrentWebviewWindow();
// appWebview.listen<string>("helios/rust-event", (event) => {
// 	if (event.payload === "app-settings") {
// 		console.log("Open settings");
// 	}
// });
