"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";

export type LogLevel = "info" | "warn" | "error" | "debug" | "success";

export interface LogEntry {
	id: string;
	timestamp: Date;
	level: LogLevel;
	message: string;
	details?: any;
	source?: string;
}

interface LoggerState {
	logs: LogEntry[];
	isVisible: boolean;
	maxLogs: number;
}

interface LoggerAction {
	addLog: (log: Omit<LogEntry, "id" | "timestamp">) => void;
	clearLogs: () => void;

	toggleLoggerView: () => void;
	setLoggerView: (val: boolean) => void;
	removeLog: (id: string) => void;
}

const initialState: LoggerState = {
	logs: [],
	isVisible: false,
	maxLogs: 1000,
};

export const useLoggerStore = create<LoggerState & LoggerAction>()(
	devtools((set, get) => ({
		...initialState,

		addLog: (log: Omit<LogEntry, "id" | "timestamp">) => {
			const newLog: LogEntry = {
				...log,
				id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
				timestamp: new Date(),
			};
			const updatedLogs = [newLog, ...get().logs].slice(0, get().maxLogs);
			set({ logs: updatedLogs });
		},
		clearLogs: () => set({ logs: [] }),
		toggleLoggerView: () => set({ isVisible: !get().isVisible }),
		setLoggerView: (val: boolean) => set({ isVisible: val }),
		removeLog: (id: string) =>
			set({ logs: get().logs.filter((log) => log.id !== id) }),
	})),
);

const log = (
	level: LogLevel,
	message: string,
	details?: any,
	source?: string,
) => {
	useLoggerStore.getState().addLog({ level, message, details, source });
};

export const info = (message: string, details?: any, source?: string) => {
	log("info", message, details, source);
};

export const warn = (message: string, details?: any, source?: string) => {
	log("warn", message, details, source);
};

export const error = (message: string, details?: any, source?: string) => {
	log("error", message, details, source);
};

export const debug = (message: string, details?: any, source?: string) => {
	log("debug", message, details, source);
};

export const success = (message: string, details?: any, source?: string) => {
	log("success", message, details, source);
};
