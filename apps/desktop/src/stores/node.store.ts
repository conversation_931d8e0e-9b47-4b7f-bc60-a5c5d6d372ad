import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { useShallow } from "zustand/shallow";
import type { Node } from "@xyflow/react";
import { type NodeStatus, NodeType } from "../types/pipeline.types";

interface NodeState {
	// Node status tracking
	nodeStatuses: Record<string, NodeStatus>;
	processingNodes: Set<string>;

	// Node counters for generating IDs
	nodeCounter: number;

	// Node templates
	nodeTemplates: Record<NodeType, Partial<Node>>;
}

interface NodeActions {
	// Status management
	setNodeStatus: (nodeId: string, status: NodeStatus) => void;
	startProcessing: (nodeId: string) => void;
	stopProcessing: (nodeId: string, status: NodeStatus) => void;
	resetNodeStatus: (nodeId: string) => void;

	// Node creation
	getNextNodeId: () => string;
	createNode: (
		type: NodeType,
		position: { x: number; y: number },
		data?: any,
	) => Node;
	createNodeWithId: (
		id: string,
		type: NodeType,
		position: { x: number; y: number },
		data?: any,
	) => Node;

	// Template management
	addNodeTemplate: (type: NodeType, template: Partial<Node>) => void;
	updateNodeTemplate: (type: NodeType, updates: Partial<Node>) => void;
}

// Initial state
const initialState: NodeState = {
	nodeStatuses: {},
	processingNodes: new Set<string>(),
	nodeCounter: 0,
	nodeTemplates: {
		[NodeType.DEFAULT]: {
			type: NodeType.DEFAULT,
			measured: { height: 120, width: 40 },
			data: { label: "Default Node" },
		},
		[NodeType.INPUT]: {
			type: NodeType.INPUT,
			measured: { height: 96, width: 200 },
			data: { label: "Input" },
		},
		[NodeType.OUTPUT]: {
			type: NodeType.OUTPUT,
			measured: { height: 96, width: 200 },
			data: { label: "Output" },
		},
		[NodeType.TOOL_CUSTOM]: {
			type: NodeType.TOOL_CUSTOM,
			measured: { height: 120, width: 40 },
			data: {
				toolbarVisible: true,
				toolbarPosition: "center",
				label: "Custom Toolbar Node",
			},
		},
		[NodeType.HANDLE_CUSTOM]: {
			type: NodeType.HANDLE_CUSTOM,
			measured: { height: 120, width: 40 },
			data: { label: "Custom Handle Node" },
		},
		[NodeType.INDICATOR]: {
			type: NodeType.INDICATOR,
			measured: {
				height: 110,
				width: 180,
			},
			data: { label: "Indicator Node" },
		},
		[NodeType.TRANSFORM]: {
			type: NodeType.INDICATOR,
			measured: {
				height: 110,
				width: 180,
			},
			data: { label: "Indicator Node" },
		},
		[NodeType.OPERATOR]: {
			type: NodeType.INDICATOR,
			measured: {
				height: 110,
				width: 180,
			},
			data: { label: "Indicator Node" },
		},
	},
};

export const useNodeStore = create<NodeState & NodeActions>()(
	devtools((set, get) => ({
		...initialState,

		// Status management
		setNodeStatus: (nodeId, status) =>
			set((state) => ({
				nodeStatuses: { ...state.nodeStatuses, [nodeId]: status },
			})),

		startProcessing: (nodeId) =>
			set((state) => {
				const newProcessingNodes = new Set(state.processingNodes);
				if (!newProcessingNodes.has(nodeId)) {
					newProcessingNodes.add(nodeId);
					return {
						processingNodes: newProcessingNodes,
						nodeStatuses: { ...state.nodeStatuses, [nodeId]: "loading" },
					};
				}

				return { processingNodes: newProcessingNodes };
			}),

		stopProcessing: (nodeId, status) =>
			set((state) => {
				const newProcessingNodes = new Set(state.processingNodes);
				newProcessingNodes.delete(nodeId);
				return {
					processingNodes: newProcessingNodes,
					nodeStatuses: { ...state.nodeStatuses, [nodeId]: status },
				};
			}),

		resetNodeStatus: (nodeId) =>
			set((state) => {
				const newProcessingNodes = new Set(state.processingNodes);
				newProcessingNodes.delete(nodeId);
				return {
					processingNodes: newProcessingNodes,
					nodeStatuses: { ...state.nodeStatuses, [nodeId]: "initial" },
				};
			}),

		// Node creation
		getNextNodeId: () => {
			const nextId = String(get().nodeCounter + 1);
			set((state) => ({ nodeCounter: state.nodeCounter + 1 }));
			return nextId;
		},

		createNode: (type, position, data = {}) => {
			const id = get().getNextNodeId();
			const template = get().nodeTemplates[type] || {};

			return {
				id,
				position,
				type,
				...template,
				data: {
					...template.data,
					...data,
					label: data.label || `${type} ${id}`,
				},
			} as Node;
		},

		createNodeWithId: (id, type, position, data = {}) => {
			const template = get().nodeTemplates[type] || {};

			return {
				id,
				position,
				type,
				...template,
				data: {
					...template.data,
					...data,
					label: data.label || `${type} ${id}`,
				},
			} as Node;
		},

		// Template management
		addNodeTemplate: (type, template) =>
			set((state) => ({
				nodeTemplates: {
					...state.nodeTemplates,
					[type]: template,
				},
			})),

		updateNodeTemplate: (type, updates) =>
			set((state) => ({
				nodeTemplates: {
					...state.nodeTemplates,
					[type]: {
						...state.nodeTemplates[type],
						...updates,
						data: {
							...state.nodeTemplates[type]?.data,
							...updates.data,
						},
					},
				},
			})),
	})),
);

// Selector hooks for better performance
export const useNodeStatus = (nodeId: string) =>
	useNodeStore((state) => state.nodeStatuses[nodeId] || "initial");

export const useIsNodeProcessing = (nodeId: string) =>
	useNodeStore((state) => state.processingNodes.has(nodeId));

export const useNodeTemplates = () =>
	useNodeStore((state) => state.nodeTemplates);

export const useNodeActions = () =>
	useNodeStore(
		useShallow((state) => ({
			setStatus: state.setNodeStatus,
			startProcessing: state.startProcessing,
			stopProcessing: state.stopProcessing,
			resetStatus: state.resetNodeStatus,
			createNode: state.createNode,
		})),
	);
