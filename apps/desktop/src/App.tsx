import * as React from "react";
import "./styles/main.css";

// import { getCurrentWindow } from "@tauri-apps/api/window";
// import { invoke } from "@tauri-apps/api/core";
import PipelinePage from "@pages/PipelinePage";
import { invoke } from "@tauri-apps/api/core";
import { ask } from "@tauri-apps/plugin-dialog";
import { Button } from "@components/ui/button";
import { handleNotifications } from "@components/testNotifiy";

// Control the current window
// const appWindow = getCurrentWindow();

function App() {
	const [msg, setGreetMsg] = React.useState("");

	async function greet() {
		// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
		setGreetMsg(await invoke("greet", { name: "Efren" }));

		await handleNotifications();

		const yes = await ask(
			`
	Update to something is available!
	Release notes: v2.0
			  `,
			{
				title: "Update Now!",
				kind: "info",
				okLabel: "Update",
				cancelLabel: "Cancel",
			},
		);

		if (yes) {
			setGreetMsg(`${msg} Upadte now`);
		}
	}

	return (
		<main className="h-dvh w-dvw bg-gray-50 overflow-hidden">
			{/* Titlebar for window dragging */}
			{/* <div
				className="w-full absolute top-0 left-0 h-10 bg-gray-800 text-white flex items-center px-4 select-none z-50"
				data-tauri-drag-region
			>
				<span className="font-medium pointer-events-none">
					Pipeline Manager
				</span>
			</div> */}

			{/* <Button variant="default" onClick={greet}>
				Push me
			</Button>
			<p>{msg}</p> */}

			{/* Main content with padding for titlebar */}
			<PipelinePage />
		</main>
	);
}

export default App;
