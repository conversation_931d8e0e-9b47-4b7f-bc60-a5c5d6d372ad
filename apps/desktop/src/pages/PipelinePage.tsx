import * as React from "react";
import { FlowCanvasWithProvider } from "@components/pipeline/FlowCanvas";
import NodeSidebar from "@components/pipeline/NodeSidebar";
import { Dn<PERSON>rovider } from "../utils/dndContext";
import AppHeader from "@components/ui/AppHeader";
import { ConsolePanel } from "@components/ui/logger/ConsolePanel";
import { LoggerTestPanel } from "@components/ui/LoggerTestPanel";

/**
 * PipelinePage is the main page for the pipeline editor
 */

export default function PipelinePage() {
	// Initialize the pipeline with some sample nodes
	// React.useEffect(() => {
	// 	if (nodes.length === 0) {
	// 		// Create some sample nodes
	// 		const node1 = createNode(
	// 			NodeType.INDICATOR,
	// 			{ x: 100, y: 100 },
	// 			{ label: "Input Node" },
	// 		);
	// 		const node2 = createNode(
	// 			NodeType.INDICATOR,
	// 			{ x: 400, y: 100 },
	// 			{ label: "Process Node" },
	// 		);
	// 		const node3 = createNode(
	// 			NodeType.HANDLE_CUSTOM,
	// 			{ x: 700, y: 100 },
	// 			{ label: "Output Node" },
	// 		);

	// 		// Add nodes to the pipeline
	// 		addNode(node1);
	// 		addNode(node2);
	// 		addNode(node3);

	// 		// Add edges between nodes
	// 		addEdge({
	// 			id: `${node1.id}-${node2.id}`,
	// 			source: node1.id,
	// 			target: node2.id,
	// 			type: "custom-edge",
	// 			data: {
	// 				source: node1.id,
	// 				target: node2.id,
	// 			},
	// 		});

	// 		addEdge({
	// 			id: `${node2.id}-${node3.id}`,
	// 			source: node2.id,
	// 			target: node3.id,
	// 			type: "custom-edge",
	// 			data: {
	// 				source: node2.id,
	// 				target: node3.id,
	// 			},
	// 		});

	// 		// Apply layout
	// 		setLayoutDirection(LayoutDirection.HORIZONTAL);
	// 		applyLayout();
	// 	}
	// }, [createNode, applyLayout, setLayoutDirection]);

	return (
		<div className="h-full overflow-hidden flex flex-col">
			{/* <PipelineUploader /> */}
			<AppHeader />
			<div className="flex flex-1 overflow-hidden">
				<DnDProvider>
					<NodeSidebar />
					<FlowCanvasWithProvider />
				</DnDProvider>
			</div>
			<ConsolePanel />
		</div>
	);
}
