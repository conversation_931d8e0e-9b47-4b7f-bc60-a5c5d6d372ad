import * as React from "react";
import type { Node, NodeProps } from "@xyflow/react";
import { NodeHandles } from "@components/nodes/NodeHandles";
import { useNodeActions, useNodeStatus } from "@stores/node.store";
import { usePipelineExecution, usePipelineStore } from "@stores/pipeline.store";
import NutIndicatorWrapper from "@components/ui/NutIndicatorWrapper";
import type { Nuts } from "../../types/nut.types";
import { useExecutionStore } from "@stores/execution.store";

const NutWithIndicator = (props: NodeProps<Node<Nuts, "indicator">>) => {
	const { edges, setEdges } = usePipelineStore();

	const status = useNodeStatus(props.id);
	const isProcessing = React.useMemo(() => status === "loading", [status]);
	const { resetStatus: resetStatusFn } = useNodeActions();

	const { executeNode } = useExecutionStore();

	// Update connected edges when status changes
	React.useEffect(() => {
		const outgoingEdges = edges.filter((edge) => edge.source === props.id);

		if (outgoingEdges.length > 0) {
			setEdges((edges) =>
				edges.map((edge) => {
					if (edge.source === props.id) {
						return {
							...edge,
							data: {
								...edge.data,
								status: status === "loading" ? undefined : status,
								isRunning: status === "loading",
								label: status === "error" ? "Error" : undefined,
							},
						};
					}
					return edge;
				}),
			);
		}
	}, [status, props.id]);

	// Simulate a processing action
	const simulateProcessing = (id: string) => {
		executeNode(id);
	};

	// Reset status to initial
	const resetStatus = () => {
		resetStatusFn(props.id);
	};

	return (
		<NutIndicatorWrapper {...props}>
			<>
				{/* Node Actions */}
				<div className="flex gap-2 justify-between">
					<button
						type="button"
						onClick={() => simulateProcessing(props.id)}
						disabled={isProcessing}
						className={`text-xs px-3 py-1.5 rounded-md flex-1 transition-colors ${
							isProcessing
								? "bg-gray-100 text-gray-400 cursor-not-allowed"
								: "bg-blue-500 text-white hover:bg-blue-600"
						}`}
					>
						{isProcessing ? "Processing..." : "Process"}
					</button>

					<button
						type="button"
						onClick={resetStatus}
						disabled={status === "initial" || isProcessing}
						className={`text-xs px-3 py-1.5 rounded-md flex-1 transition-colors ${
							status === "initial" || isProcessing
								? "bg-gray-100 text-gray-400 cursor-not-allowed"
								: "bg-gray-500 text-white hover:bg-gray-600"
						}`}
					>
						Reset
					</button>
				</div>

				<NodeHandles />
			</>
		</NutIndicatorWrapper>
	);
};

export default NutWithIndicator;
