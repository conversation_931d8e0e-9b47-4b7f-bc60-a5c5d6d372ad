import { useState } from "react";
import { ChevronDown, ChevronRight, <PERSON>, <PERSON><PERSON>, Check } from "lucide-react";
import { Button } from "@components/ui/button";
import type { LogEntry, LogLevel } from "@stores/log.store";

interface LogEntryProps {
	log: LogEntry;
	onRemove: (id: string) => void;
}

const getLevelIcon = (level: LogLevel) => {
	switch (level) {
		case "info":
			return "ℹ️";
		case "warn":
			return "⚠️";
		case "error":
			return "❌";
		case "debug":
			return "🐛";
		case "success":
			return "✅";
		default:
			return "📝";
	}
};

const getLevelColor = (level: LogLevel) => {
	switch (level) {
		case "info":
			return "text-blue-400";
		case "warn":
			return "text-yellow-400";
		case "error":
			return "text-red-400";
		case "debug":
			return "text-purple-400";
		case "success":
			return "text-green-400";
		default:
			return "text-gray-400";
	}
};

const getLevelBg = (level: Log<PERSON>evel) => {
	switch (level) {
		case "info":
			return "bg-blue-500/10";
		case "warn":
			return "bg-yellow-500/10";
		case "error":
			return "bg-red-500/10";
		case "debug":
			return "bg-purple-500/10";
		case "success":
			return "bg-green-500/10";
		default:
			return "bg-gray-500/10";
	}
};

export function LogEntryComponent({ log, onRemove }: LogEntryProps) {
	const [isExpanded, setIsExpanded] = useState(false);
	const [copied, setCopied] = useState(false);

	const hasDetails = log.details !== undefined && log.details !== null;

	const formatTime = (date: Date) => {
		return date.toLocaleTimeString("en-US", {
			hour12: false,
			hour: "2-digit",
			minute: "2-digit",
			second: "2-digit",
			// fractionalSecondDigits: 3,
		});
	};

	const copyToClipboard = async () => {
		const logText = `[${formatTime(log.timestamp)}] ${log.level.toUpperCase()}: ${log.message}${
			hasDetails ? `\nDetails: ${JSON.stringify(log.details, null, 2)}` : ""
		}`;

		try {
			await navigator.clipboard.writeText(logText);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy log:", err);
		}
	};

	return (
		<div
			className={`group border-l-2 border-l-transparent hover:border-l-gray-500 ${getLevelBg(log.level)} hover:bg-gray-800/50 transition-colors`}
		>
			<div className="flex items-start gap-2 p-2 font-mono text-sm">
				{/* Expand/Collapse Button */}
				{hasDetails && (
					<button
						type="button"
						onClick={() => setIsExpanded(!isExpanded)}
						className="mt-0.5 text-gray-500 hover:text-gray-300 transition-colors"
					>
						{isExpanded ? (
							<ChevronDown className="w-3 h-3" />
						) : (
							<ChevronRight className="w-3 h-3" />
						)}
					</button>
				)}

				{/* Spacer for logs without details */}
				{!hasDetails && <div className="w-3" />}

				{/* Timestamp */}
				<span className="text-gray-500 text-xs mt-0.5 min-w-[80px]">
					{formatTime(log.timestamp)}
				</span>

				{/* Level */}
				<span
					className={`${getLevelColor(log.level)} font-semibold min-w-[60px] text-xs mt-0.5`}
				>
					{getLevelIcon(log.level)} {log.level.toUpperCase()}
				</span>

				{/* Source */}
				{log.source && (
					<span className="text-gray-400 text-xs mt-0.5 min-w-[80px]">
						[{log.source}]
					</span>
				)}

				{/* Message */}
				<span className="text-gray-200 flex-1 break-words">{log.message}</span>

				{/* Actions */}
				<div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
					<Button
						variant="ghost"
						size="sm"
						onClick={copyToClipboard}
						className="h-6 w-6 p-0 text-gray-500 hover:text-gray-300"
					>
						{copied ? (
							<Check className="w-3 h-3" />
						) : (
							<Copy className="w-3 h-3" />
						)}
					</Button>
					<Button
						variant="ghost"
						size="sm"
						onClick={() => onRemove(log.id)}
						className="h-6 w-6 p-0 text-gray-500 hover:text-red-400"
					>
						<X className="w-3 h-3" />
					</Button>
				</div>
			</div>

			{/* Details */}
			{hasDetails && isExpanded && (
				<div className="px-8 pb-2">
					<div className="bg-gray-900 rounded p-2 border border-gray-700">
						<pre className="text-xs text-gray-300 whitespace-pre-wrap overflow-x-auto">
							{JSON.stringify(log.details, null, 2)}
						</pre>
					</div>
				</div>
			)}
		</div>
	);
}
