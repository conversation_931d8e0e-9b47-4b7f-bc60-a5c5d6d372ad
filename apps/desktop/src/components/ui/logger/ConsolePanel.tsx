import { useRef, useEffect, useState } from "react";
import {
	ChevronUp,
	ChevronDown,
	Terminal,
	Trash2,
	Search,
	Download,
	Filter,
} from "lucide-react";
import { Button } from "@components/ui/button";
import {
	useLoggerStore,
	type LogLevel,
	type LogCategory,
} from "@stores/log.store";
import { LogEntryComponent } from "./LogEntry";

const levelFilters: {
	value: LogLevel | "all";
	label: string;
	color: string;
}[] = [
	{ value: "all", label: "All", color: "text-gray-400" },
	{ value: "info", label: "Info", color: "text-blue-400" },
	{ value: "warn", label: "Warn", color: "text-yellow-400" },
	{ value: "error", label: "Error", color: "text-red-400" },
	{ value: "debug", label: "Debug", color: "text-purple-400" },
	{ value: "success", label: "Success", color: "text-green-400" },
];

export function ConsolePanel() {
	const logs = useLoggerStore((state) => state.logs);
	const isVisible = useLoggerStore((state) => state.isVisible);
	const toggleVisibility = useLoggerStore((state) => state.toggleLoggerView);
	const clear = useLoggerStore((state) => state.clearLogs);
	const removeLog = useLoggerStore((state) => state.removeLog);

	const [filterLevel, setFilterLevel] = useState<LogLevel | "all">("all");
	const [filterCategory, setFilterCategory] = useState<LogCategory | "all">(
		"all",
	);
	const [filterCategory, setFilterCategory] = useState<LogCategory | "all">(
		"all",
	);
	const [searchTerm, setSearchTerm] = useState("");
	const [autoScroll, setAutoScroll] = useState(true);
	const logsEndRef = useRef<HTMLDivElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	const filteredLogs = logs.filter((log) => {
		const matchesLevel = filterLevel === "all" || log.level === filterLevel;
		const matchesSearch =
			searchTerm === "" ||
			log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
			log.source?.toLowerCase().includes(searchTerm.toLowerCase());
		return matchesLevel && matchesSearch;
	});

	useEffect(() => {
		if (autoScroll && logsEndRef.current) {
			logsEndRef.current.scrollIntoView({ behavior: "smooth" });
		}
	}, [filteredLogs, autoScroll]);

	const handleScroll = () => {
		if (scrollContainerRef.current) {
			const { scrollTop, scrollHeight, clientHeight } =
				scrollContainerRef.current;
			const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
			setAutoScroll(isAtBottom);
		}
	};

	return (
		<div
			className={`bg-gray-950 border-t border-gray-700 transition-all duration-300 ${
				isVisible ? "h-80" : "h-10"
			}`}
		>
			{/* Header */}
			<div className="flex items-center justify-between px-4 py-2 bg-gray-900 border-b border-gray-700">
				<div className="flex items-center gap-3">
					<Button
						variant="ghost"
						size="sm"
						onClick={toggleVisibility}
						className="text-gray-400 hover:text-gray-200 p-1"
					>
						<Terminal className="w-4 h-4 mr-2" />
						Console
						{isVisible ? (
							<ChevronDown className="w-4 h-4 ml-2" />
						) : (
							<ChevronUp className="w-4 h-4 ml-2" />
						)}
					</Button>

					{isVisible && (
						<>
							<div className="h-4 w-px bg-gray-700" />
							<span className="text-xs text-gray-500">
								{filteredLogs.length} of {logs.length} logs
							</span>
						</>
					)}
				</div>

				{isVisible && (
					<div className="flex items-center gap-2">
						{/* Search */}
						<div className="relative">
							<Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-500" />
							<input
								type="text"
								placeholder="Search logs..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="bg-gray-800 border border-gray-600 rounded px-7 py-1 text-xs text-gray-200 placeholder-gray-500 focus:outline-none focus:border-blue-500 w-40"
							/>
						</div>

						{/* Level Filter */}
						<select
							value={filterLevel}
							onChange={(e) =>
								setFilterLevel(e.target.value as LogLevel | "all")
							}
							className="bg-gray-800 border border-gray-600 rounded px-2 py-1 text-xs text-gray-200 focus:outline-none focus:border-blue-500"
						>
							{levelFilters.map((filter) => (
								<option key={filter.value} value={filter.value}>
									{filter.label}
								</option>
							))}
						</select>

						{/* Auto-scroll toggle */}
						<Button
							variant="ghost"
							size="sm"
							onClick={() => setAutoScroll(!autoScroll)}
							className={`text-xs p-1 h-6 ${autoScroll ? "text-green-400" : "text-gray-500"}`}
						>
							Auto-scroll
						</Button>

						{/* Clear */}
						<Button
							variant="ghost"
							size="sm"
							onClick={clear}
							className="text-gray-400 hover:text-red-400 p-1"
							disabled={logs.length === 0}
						>
							<Trash2 className="w-3 h-3" />
						</Button>
					</div>
				)}
			</div>

			{/* Logs Content */}
			{isVisible && (
				<div
					ref={scrollContainerRef}
					onScroll={handleScroll}
					className="h-full overflow-y-auto bg-gray-950"
				>
					{filteredLogs.length === 0 ? (
						<div className="flex items-center justify-center h-full text-gray-500 text-sm">
							{logs.length === 0
								? "No logs yet"
								: "No logs match current filters"}
						</div>
					) : (
						<div className="divide-y divide-gray-800">
							{filteredLogs.map((log) => (
								<LogEntryComponent
									key={log.id}
									log={log}
									onRemove={removeLog}
								/>
							))}
							<div ref={logsEndRef} />
						</div>
					)}
				</div>
			)}
		</div>
	);
}
