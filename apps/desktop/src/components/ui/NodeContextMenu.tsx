import * as React from "react";
import { <PERSON><PERSON>, Trash2, Edit, Lock, Unlock, Info } from "lucide-react";
import { useContextMenu } from "../../stores/ui.store";
import { usePipelineStore } from "../../stores/pipeline.store";
import { useEditDialog } from "../../stores/ui.store";
import { Button } from "./button";

interface NodeContextMenuProps {
	className?: string;
}

/**
 * NodeContextMenu provides a context menu for node operations
 */
export const NodeContextMenu: React.FC<NodeContextMenuProps> = ({
	className = "",
}) => {
	const { nodeId, position, isVisible, hide } = useContextMenu();
	const { nodes, removeNode, addNode } = usePipelineStore();
	const { open: openEditDialog } = useEditDialog();

	// Find the node by ID
	const node = React.useMemo(() => {
		return nodes.find((n) => n.id === nodeId);
	}, [nodeId, nodes]);

	// Handle edit action
	const handleEdit = React.useCallback(() => {
		if (node) {
			openEditDialog(node);
			hide();
		}
	}, [node, openEditDialog, hide]);

	// Handle delete action
	const handleDelete = React.useCallback(() => {
		if (nodeId) {
			removeNode(nodeId);
			hide();
		}
	}, [nodeId, removeNode, hide]);

	// Handle duplicate action
	const handleDuplicate = React.useCallback(() => {
		const node = nodes.filter((node) => node.id === nodeId)[0];

		const position = {
			x: node.position.x,
			y: node.position.y + 50,
		};

		addNode({
			...node,
			selected: false,
			dragging: false,
			id: `${nodeId}-copy`,
			position,
			data: {
				...node.data,
				label: `${node.data?.label || node.id} (copy)`,
			},
		});

		hide();
	}, [hide]);

	// If no node is selected or menu is not visible, don't render
	if (!isVisible || !node) {
		return null;
	}

	const menuRef = React.useRef<HTMLDivElement>(null);

	// Close menu when clicking outside
	React.useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				// Animate out before closing
				hide();
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const buttonClass =
		"w-full text-left justify-start px-3 py-1 text-xs text-gray-700 !border-0 !rounded-0 gap-2";
	const btnDefault = "hover:bg-blue-50 hover:text-blue-700";
	const btnDelete = "text-red-600 !border-0 hover:bg-red-50 hover:text-red-700";
	const iconSize = 12;

	// Position the menu exactly at the click position
	const menuStyle: React.CSSProperties = {
		opacity: isVisible ? 1 : 0,
		transform: isVisible ? "scale(1)" : "scale(0.85)",
		transition: "opacity 200ms ease-in-out, transform 200ms ease-in-out",
	};

	return (
		<div
			ref={menuRef}
			className={`absolute z-[100] max-w-[160px] rounded-md border bg-popover p-1 shadow-md ${className}`}
			style={{
				...menuStyle,
				left: position.left,
				top: position.top,
				right: position.right,
				bottom: position.bottom,
			}}
		>
			<div className="p-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md">
				<p className="text-xs font-medium text-gray-500">
					Node:{" "}
					<span className="font-bold text-gray-700">
						{(node.data?.label as string) || node.id}
					</span>
				</p>
				{node?.type && (
					<p className="text-xs text-gray-500">
						Type: <span className="font-medium">{node.type}</span>
					</p>
				)}
			</div>

			<div className="pt-1">
				<Button
					type="button"
					variant="ghost"
					onClick={handleDuplicate}
					className={`${buttonClass} ${btnDefault}`}
				>
					<Copy size={iconSize} />
					<span>Duplicate</span>
				</Button>

				<Button
					type="button"
					variant="ghost"
					onClick={handleEdit}
					className={`${buttonClass} ${btnDefault}`}
				>
					<Edit size={iconSize} />
					<span>Edit</span>
				</Button>

				{/* <button type="button" onClick={toggleLock} className={`${buttonClass}` ${btnDefault}}>
					{isLocked ? <Unlock size={iconSize} /> : <Lock size={iconSize} />}
					<span>{isLocked ? "Unlock" : "Lock"}</span>
				</button> */}

				<Button
					type="button"
					variant="ghost"
					onClick={() => alert("Properties")}
					className={`${buttonClass} ${btnDefault}`}
				>
					<Info size={iconSize} />
					<span>Properties</span>
				</Button>
			</div>

			<div className=" border-gray-200">
				<Button
					type="button"
					variant="ghost"
					onClick={handleDelete}
					className={`${buttonClass} ${btnDelete}`}
				>
					<Trash2 size={iconSize} />
					<span>Delete</span>
				</Button>
			</div>
		</div>
	);
};
