import {
	Download,
	Pause,
	Play,
	RotateCcw,
	Upload,
	Workflow,
} from "lucide-react";
import { Button } from "./button";
import {
	useExecutionControls,
	useExecutionStatus,
} from "@stores/execution.store";
import { usePipelineStore } from "@stores/pipeline.store";
import PipelineUploader from "@components/pipeline/PipelineUploader";
import { Separator } from "./separator";
import { Badge } from "./badge";

const AppHeader = () => {
	const {
		startExecution,
		pauseExecution,
		resumeExecution,
		stopExecution,
		resetExecution,
	} = useExecutionControls();
	const { isRunning, isPaused, progress } = useExecutionStatus();

	const nodes = usePipelineStore((state) => state.nodes);

	return (
		<header className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
			<div className="flex items-center gap-3">
				<div className="flex items-center gap-2">
					<Workflow className="w-6 h-6 text-blue-600" />
					<h1 className="!text-xl font-semibold text-gray-900">Helios</h1>
				</div>
				<Badge variant="secondary" className="text-xs">
					Visual Programming
				</Badge>
			</div>

			<div className="flex items-center gap-3">
				<PipelineUploader />
				<Separator orientation="vertical" className="!h-6" />
				<Button
					variant="outline"
					size="sm"
					onClick={resetExecution}
					disabled={nodes.length === 0}
				>
					<RotateCcw className="w-4 h-4 mr-2" />
					Reset
				</Button>
				{!isRunning ? (
					<Button
						variant="default"
						size="sm"
						type="button"
						onClick={startExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-green-600 hover:bg-green-700 text-white border-green-600"
					>
						<Play className="w-4 h-4 mr-2" />
						<span>Run</span>
					</Button>
				) : isPaused ? (
					<Button
						variant="outline"
						size="sm"
						type="button"
						onClick={resumeExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
					>
						<Play className="w-4 h-4 mr-2" />
						<span>Resume</span>
					</Button>
				) : (
					<Button
						variant="default"
						size="sm"
						type="button"
						onClick={pauseExecution}
						className="flex items-center gap-2 px-3 py-1 rounded-md transition-colors bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600"
					>
						<Pause className="w-4 h-4 mr-2" />
						<span>Pause</span>
					</Button>
				)}
			</div>
		</header>
	);
};

export default AppHeader;
