import * as React from "react";
import { useEditDialog, useUIStore } from "../../stores/ui.store";
import { usePipelineStore } from "../../stores/pipeline.store";
import { X } from "lucide-react";
import { Button } from "./button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "./dialog";

import { LayoutDirection } from "../../types/pipeline.types";

interface NodeEditDialogProps {
	className?: string;
}

/**
 * NodeEditDialog provides a dialog for editing node properties
 */
export const SettingsDialog: React.FC<NodeEditDialogProps> = () => {
	const { close } = useEditDialog();
	const isSettingsDialogOpen = useUIStore(
		(state) => state.isSettingsDialogOpen,
	);
	const { applyLayout, setLayoutDirection, fitView } = usePipelineStore();

	const [formData, setFormData] = React.useState({
		label: "",
		type: "",
		description: "",
	});

	// Handle form submission
	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
	};

	// Handle changing layout direction
	const handleChangeLayout = React.useCallback(
		(direction: LayoutDirection) => {
			setLayoutDirection(direction);
			applyLayout();
		},
		[setLayoutDirection, applyLayout],
	);

	// If dialog is not open or no node is selected, don't render
	if (!isSettingsDialogOpen) {
		return null;
	}

	return (
		<Dialog
			open={isSettingsDialogOpen}
			onOpenChange={(open) => !open && close()}
		>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle className="flex items-center justify-between">
						Canvas settings
						<Button
							variant="ghost"
							size="icon"
							className="!grid"
							onClick={close}
						>
							<X className="h-4 w-4" size={14} />
						</Button>
					</DialogTitle>
					<DialogDescription>
						Configure canvas properties like layout and theme
					</DialogDescription>
				</DialogHeader>

				<div className="flex flex-row gap-2 justify-center items-start">
					<Button
						variant="secondary"
						size="lg"
						type="button"
						onClick={() => {
							handleChangeLayout(LayoutDirection.VERTICAL);
							fitView({ padding: "50px" });
						}}
					>
						vertical
					</Button>
					<Button
						variant="secondary"
						size="lg"
						type="button"
						onClick={() => {
							handleChangeLayout(LayoutDirection.HORIZONTAL);
							fitView({ padding: "50px" });
						}}
					>
						horizontal
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};
