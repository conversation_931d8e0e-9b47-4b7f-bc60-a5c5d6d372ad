import * as React from "react";
import { Edit2, Trash2 } from "lucide-react";

interface EdgeToolbarProps {
	onDelete: () => void;
	onEdit?: () => void;
}

/**
 * EdgeToolbar provides controls for edge operations like delete
 */
export const EdgeToolbar: React.FC<EdgeToolbarProps> = ({
	onDelete,
	onEdit,
}) => {
	// Animation effect when toolbar appears
	React.useEffect(() => {
		const btn = document.getElementById("edge-toolbar");
		if (btn) {
			btn.style.opacity = "1";
			btn.style.transform = "scale(1)";
		}
	}, []);

	return (
		<div
			id="edge-toolbar"
			className="nodrag nopan opacity-0 scale-50 transition-all duration-300 flex gap-1 bg-white rounded-full border-8 border-main-background"
		>
			<button
				type="button"
				className="p-1 rounded-full bg-secondary hover:bg-red-100 transition-colors"
				onClick={(e) => {
					e.stopPropagation();
					onDelete();
				}}
				title="Delete connection"
			>
				<Trash2 size={14} className="text-red-500" />
			</button>

			{onEdit && (
				<button
					type="button"
					className="p-1 rounded-full bg-secondary hover:bg-blue-100 transition-colors"
					onClick={(e) => {
						e.stopPropagation();
						onEdit();
					}}
					title="Edit connection"
				>
					<Edit2 width={12} height={12} className="text-slate-500" />
				</button>
			)}
		</div>
	);
};
