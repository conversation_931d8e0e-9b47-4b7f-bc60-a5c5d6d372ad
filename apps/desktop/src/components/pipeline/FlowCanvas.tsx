import * as React from "react";
import {
	<PERSON>actFlow,
	MiniMap,
	Controls,
	Background,
	Panel,
	BackgroundVariant,
	ReactFlowProvider,
	useReactFlow,
} from "@xyflow/react";

import type {
	Edge,
	Node,
	IsValidConnection,
	NodeMouseHandler,
	OnConnect,
	OnNodesDelete,
	OnNodeDrag,
} from "@xyflow/react";

import { usePipelineStore } from "../../stores/pipeline.store";
import { useExecutionStore } from "../../stores/execution.store";
import {
	useContextMenu,
	useEditDialog,
	useSidebar,
} from "../../stores/ui.store";
import { useNodeStore } from "../../stores/node.store";
import { NodeType, type LayoutDirection } from "../../types/pipeline.types";
import { wouldCreateCycle } from "../../utils/edge.utils";

// Import node and edge components
import FlowEdge from "../edges/FlowEdge";
import ProcessingNode from "../nodes/ProcessingNode";
import { CustomNodeToolbar } from "../nodes/NutExample";
import { NodeContextMenu } from "../ui/NodeContextMenu";
import { NodeEditDialog } from "../ui/NodeEditDialog";
import { InputNut } from "@components/nodes/InputNut";
import { OutputNut } from "@components/nodes/OutputNut";
import { Button } from "../ui/button";
import ConnectionLine from "../edges/ConnectionLine";
import PipelineExecutionControl from "./PipelineExecutionControl";

import { useDnD } from "../../utils/dndContext";
import { PanelLeftClose, PanelRightClose, Workflow } from "lucide-react";
import { SettingsDialog } from "@components/ui/SettingsDialog";

// Node types mapping
const nodeTypes = {
	[NodeType.DEFAULT]: CustomNodeToolbar,
	[NodeType.INPUT]: InputNut,
	[NodeType.OUTPUT]: OutputNut,
	[NodeType.TOOL_CUSTOM]: CustomNodeToolbar,
	[NodeType.HANDLE_CUSTOM]: CustomNodeToolbar,
	[NodeType.INDICATOR]: ProcessingNode,
	[NodeType.TRANSFORM]: ProcessingNode,
	[NodeType.OPERATOR]: ProcessingNode,
};

// Edge types mapping
const edgeTypes = {
	default: FlowEdge,
	"custom-edge": FlowEdge,
};

interface FlowCanvasProps {
	onNodeClick?: (nodeId: string) => void;
	onEdgeClick?: (edgeId: string) => void;
}

/**
 * FlowCanvas is the main component for displaying and interacting with the flow diagram
 */
export const FlowCanvas: React.FC<FlowCanvasProps> = ({
	onNodeClick,
	onEdgeClick,
}) => {
	// Get nodes and edges from the pipeline store
	const pipelineStore = usePipelineStore();
	const {
		nodes,
		edges,
		onNodesChange,
		onEdgesChange,
		setFlowInstance,
		addNode,
	} = pipelineStore;

	// Local state for nodes and edges (for React Flow)
	// const [nodes, setNodes, onNodesChange] = useNodesState(storeNodes);
	// const [edges, setEdges, onEdgesChange] = useEdgesState(storeEdges);

	// React.useEffect(() => setNodes(storeNodes), [storeNodes]);
	// React.useEffect(() => setEdges(storeEdges), [storeEdges]);

	const { nodeId } = useContextMenu();

	// UI store for context menu and edit dialog
	const {
		isVisible: isVisibleContextMenu,
		show: showContextMenu,
		hide: hideContextMenu,
	} = useContextMenu();

	const { isOpen, toggle } = useSidebar();
	const { open: openEditDialog } = useEditDialog();

	// React.useEffect(() => {
	// 	setNodes(storeNodes);
	// }, [storeNodes]);

	// Node store for creating new nodes
	const { createNode } = useNodeStore();

	// Reference to the flow container
	const flowRef = React.useRef<HTMLDivElement>(null);

	// Handle connecting nodes
	const onConnect: OnConnect = React.useCallback(
		(params) => {
			// Create a custom edge with the custom edge type
			const edge = {
				...params,
				id: `${params.source}-${params.target}`,
				type: "custom-edge",
				data: {
					source: params.source,
					target: params.target,
				},
			} satisfies Edge;

			// Add the edge to the local state
			// setEdges((eds) => addEdge(edge, eds));

			// Add the edge to the store
			pipelineStore.addEdge(edge);
		},
		[pipelineStore],
	);

	const onNodeDelete: OnNodesDelete = React.useCallback((nodes: Node[]) => {
		nodes.forEach((node) => pipelineStore.removeNode(node.id));
	}, []);

	const onNodeDragStop: OnNodeDrag = React.useCallback((_, node) => {
		pipelineStore.updateNode(node.id, { position: node.position }, node.data);
	}, []);

	// Validate connections to prevent cycles
	const isValidConnection: IsValidConnection = React.useCallback(
		(connection) => {
			// Prevent self-loops
			if (connection.source === connection.target) {
				return false;
			}

			// Check if the connection would create a cycle
			return !wouldCreateCycle(
				connection.source,
				connection.target,
				edges,
				nodes,
			);
		},
		[edges, nodes],
	);

	// Handle node context menu
	const onNodeContextMenu: NodeMouseHandler = React.useCallback(
		(event, node) => {
			// Prevent native context menu from showing
			event.preventDefault();

			const sidebarWidth = 220;
			// Get the exact mouse position
			const x = event.clientX - sidebarWidth; // diff with Sidebar width
			const y = event.clientY;

			const pane = flowRef.current?.getBoundingClientRect();

			// Get viewport dimensions
			const viewportWidth = pane?.width ?? 0;
			const viewportHeight = pane?.height ?? 0;

			// Approximate menu dimensions
			const menuWidth = 160;
			const menuHeight = 200;

			// Check if menu would go off-screen
			const wouldOverflowRight = x + menuWidth > viewportWidth;
			const wouldOverflowBottom = y + menuHeight > viewportHeight;

			// Show the context menu
			showContextMenu(node.id, {
				left: wouldOverflowRight ? undefined : x,
				top: wouldOverflowBottom ? undefined : y,
				right: wouldOverflowRight ? viewportWidth - x : undefined,
				bottom: wouldOverflowBottom ? viewportHeight - y : undefined,
			});
		},
		[showContextMenu],
	);

	// Handle pane click to hide context menu
	const onPaneClick = React.useCallback(() => {
		hideContextMenu();
	}, [hideContextMenu]);

	const { screenToFlowPosition } = useReactFlow();
	const [type, setType] = useDnD();

	const onDragOver: React.DragEventHandler<HTMLDivElement> = React.useCallback(
		(event) => {
			event.preventDefault();
			event.dataTransfer.dropEffect = "move";
		},
		[],
	);

	const onDrop: React.DragEventHandler<HTMLDivElement> = React.useCallback(
		(event) => {
			event.preventDefault();

			console.log({ type });
			// check if the dropped element is valid
			if (!type) {
				return;
			}

			// project was renamed to screenToFlowPosition
			// and you don't need to subtract the reactFlowBounds.left/top anymore
			// details: https://reactflow.dev/whats-new/2023-11-10
			const position = screenToFlowPosition({
				x: event.clientX,
				y: event.clientY,
			});
			const newNode = {
				id: Math.random().toFixed(2),
				type,
				position,
				data: { label: `${type} node` },
			};
			addNode(newNode);
		},
		[screenToFlowPosition, type],
	);

	const onDragStart = (event: React.DragEvent, nodeType: NodeType) => {
		setType(nodeType);
		event.dataTransfer.setData("text/plain", nodeType);
		event.dataTransfer.effectAllowed = "move";
	};

	const [shift, setShift] = React.useState(false);
	return (
		<div className="w-full text-slate-700 flex justify-center items-center">
			<div className="w-full h-full bg-white rounded-lg shadow-md">
				<ReactFlow
					ref={flowRef}
					nodes={nodes}
					edges={edges}
					fitView
					attributionPosition="top-right"
					snapToGrid
					minZoom={0.5}
					maxZoom={2}
					proOptions={{ hideAttribution: true }}
					defaultEdgeOptions={{
						type: "custom-edge",
					}}
					connectionRadius={30}
					connectionLineComponent={ConnectionLine}
					elevateEdgesOnSelect
					onInit={setFlowInstance}
					zoomOnScroll={!shift}
					onKeyDownCapture={(evt) => {
						setShift(evt.shiftKey);
					}}
					onKeyUpCapture={(_) => {
						if (shift) setShift(false);
					}}
					onNodesChange={onNodesChange}
					onEdgesChange={onEdgesChange}
					onNodeContextMenu={onNodeContextMenu}
					onNodeDoubleClick={(_, node) => openEditDialog(node)}
					onPaneClick={onPaneClick}
					onConnect={onConnect}
					isValidConnection={isValidConnection}
					onNodesDelete={onNodeDelete}
					onNodeDrag={onNodeDragStop}
					onNodeDragStop={onNodeDragStop}
					onDrop={onDrop}
					// onDragStart={onDragStart}
					onDragOver={onDragOver}
					nodeTypes={nodeTypes}
					edgeTypes={edgeTypes}
					onNodeClick={(_, node) => {
						isVisibleContextMenu && hideContextMenu();
						onNodeClick?.(node.id);
					}}
					onEdgeClick={(_, edge) => onEdgeClick?.(edge.id)}
				>
					<Panel position="top-left" className="gap-1 flex flex-row">
						<Button
							variant="ghost"
							size="lg"
							type="button"
							onClick={toggle}
							className="!w-12 group hover:bg-transparent !grid !place-content-center"
						>
							{isOpen ? (
								<PanelLeftClose
									size={18}
									className="group-hover:stroke-blue-500"
								/>
							) : (
								<PanelRightClose
									size={18}
									className="group-hover:stroke-blue-500"
								/>
							)}
						</Button>
					</Panel>

					{/* <Panel position="top-right" className="gap-1 flex flex-row">
						<PipelineExecutionControl />
					</Panel> */}

					<Controls showZoom orientation="horizontal" />
					<MiniMap style={{ width: 120, height: 120 }} />
					<Background
						variant={BackgroundVariant.Dots}
						gap={12}
						size={1}
						bgColor="var(--color-main-background)"
					/>

					{/* Node context menu and edit dialog are rendered by the UI store */}
					{nodeId.length > 0 && <NodeContextMenu />}
					<NodeEditDialog />
					<SettingsDialog />

					{nodes.length === 0 && (
						<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
							<div className="text-center">
								<Workflow className="w-16 h-16 text-slate-500 mx-auto mb-4" />
								<h3 className="text-lg font-medium text-gray-900 mb-2">
									Start Building Your Workflow
								</h3>
								<p className="text-gray-500 max-w-sm">
									Drag nodes from the library to create your visual programming
									workflow
								</p>
							</div>
						</div>
					)}
				</ReactFlow>
			</div>
		</div>
	);
};

/**
 * FlowCanvasWithProvider wraps FlowCanvas with ReactFlowProvider
 */
export const FlowCanvasWithProvider: React.FC<FlowCanvasProps> = (props) => {
	return (
		<ReactFlowProvider>
			<FlowCanvas {...props} />
		</ReactFlowProvider>
	);
};

export default FlowCanvasWithProvider;
