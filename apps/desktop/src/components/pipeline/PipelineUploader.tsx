import * as React from "react";
import { invoke } from "@tauri-apps/api/core";
import { open } from "@tauri-apps/plugin-dialog";
import { readTextFile } from "@tauri-apps/plugin-fs";
import { usePipelineStore } from "@stores/pipeline.store";
import { useNodeStore } from "@stores/node.store";
import { LayoutDirection, NodeType } from "../../types/pipeline.types";
import { Button } from "@components/ui/button";
import { Download, Upload } from "lucide-react";

interface ValidationResult {
	is_valid: boolean;
	errors: string[];
	pipeline?: any;
}

interface PipelineStructure {
	nodes: Array<{
		id: string;
		nut_type: NodeType;
		description?: string;
		input_types: string[];
		output_types: string[];
	}>;
	edges: Array<{
		source: string;
		target: string;
	}>;
}

interface PipelineUploaderProps {
	showTitle?: boolean;
	title?: string;
	showStructure?: boolean;
}

export default function PipelineUploader(props: PipelineUploaderProps) {
	const fileName = usePipelineStore((state) => state.fileName);
	const setFileName = usePipelineStore((state) => state.setPipelineFile);

	usePipelineStore.subscribe(
		(state) => state.fileName,
		(fileName, _) => {
			readTextFile(fileName).then((content) => {
				setFile(content);

				// Reset states
				setValidationResult(null);
				setPipelineStructure(null);
				setError(null);
			});
		},
	);

	const [file, setFile] = React.useState<string | null>(null);
	const [validationResult, setValidationResult] =
		React.useState<ValidationResult | null>(null);
	const [pipelineStructure, setPipelineStructure] =
		React.useState<PipelineStructure | null>(null);
	const [loading, setLoading] = React.useState<boolean>(false);
	const [error, setError] = React.useState<string | null>(null);

	const { setNodes, setEdges, applyLayout, setLayoutDirection } =
		usePipelineStore();
	const { createNodeWithId } = useNodeStore();

	React.useEffect(() => {
		const nuts = pipelineStructure?.nodes.map((node) =>
			// Node type should be: node.nut_type
			createNodeWithId(
				node.id,
				node.nut_type || NodeType.INDICATOR,
				{ x: 0, y: 0 },
				node,
			),
		);
		const edges = pipelineStructure?.edges.map((edge) => ({
			...edge,
			type: "custom-edge",
			id: `${edge.source}--${edge.target}`,
		}));

		setNodes(nuts ?? []);
		setEdges(edges ?? []);
		setLayoutDirection(LayoutDirection.HORIZONTAL);
		applyLayout();
	}, [pipelineStructure]);

	const handleFileSelect = async () => {
		try {
			// Open file dialog
			const selected = await open({
				multiple: false,
				filters: [{ name: "JSON", extensions: ["json"] }],
			});

			if (selected && !Array.isArray(selected)) {
				setFileName(selected);

				// Read file content
				const content = await readTextFile(selected);
				setFile(content);

				// Reset states
				setValidationResult(null);
				setPipelineStructure(null);
				setError(null);

				validateFile();
				// validatePipeline();
			}
		} catch (err) {
			setError(`Error selecting file: ${err}`);
		}
	};

	const validatePipeline = async () => {
		if (!file) {
			setError("No file selected");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// Validate the pipeline JSON
			const result: ValidationResult = await invoke("validate_pipeline_json", {
				jsonStr: file,
			});
			setValidationResult(result);

			console.log({ result });

			// If valid, build the pipeline structure
			if (result.is_valid) {
				const structure: PipelineStructure = await invoke(
					"build_pipeline_structure_from_json",
					{ jsonStr: file },
				);
				setPipelineStructure(structure);
				console.log({ structure });
			}
		} catch (err) {
			setError(`Error validating pipeline: ${err}`);
		} finally {
			setLoading(false);
		}
	};

	const validateFile = async () => {
		if (!fileName) {
			setError("No file selected");
			return;
		}

		setLoading(true);
		setError(null);

		try {
			// Validate the pipeline file
			const result: ValidationResult = await invoke("validate_pipeline_file", {
				filePath: fileName,
			});
			setValidationResult(result);

			// If valid, build the pipeline structure
			if (result.is_valid) {
				const structure: PipelineStructure = await invoke(
					"build_pipeline_structure",
					{ filePath: fileName },
				);
				setPipelineStructure(structure);
			}
		} catch (err) {
			setError(`Error validating pipeline file: ${err}`);
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="flex flex-row gap-3">
			{props.showTitle ? <h2>{props.title ?? "Pipeline Uploader"}</h2> : null}

			<Button
				variant="outline"
				size="sm"
				onClick={handleFileSelect}
				disabled={loading}
			>
				<Upload className="w-4 h-4 mr-2" />
				Import
			</Button>
			<Button variant="outline" size="sm">
				<Download className="w-4 h-4 mr-2" />
				Export
			</Button>

			{/* <div className="upload-section">
				<Button variant="default" onClick={handleFileSelect} disabled={loading}>
					Select Pipeline JSON File
				</Button>

				{fileName && (
					<div className="flex flex-col flex-wrap gap-3 mt-3">
						<p>Selected file: {fileName}</p>
						<div className="flex flex-row gap-3 justify-center items-center">
							<Button
								variant="outline"
								onClick={validateFile}
								disabled={loading}
							>
								Validate File
							</Button>
							<Button
								variant="outline"
								onClick={validatePipeline}
								disabled={loading || !file}
							>
								Validate JSON Content
							</Button>
						</div>
					</div>
				)}

				{loading && <p>Loading...</p>}

				{error && (
					<div className="text-red-600 p-2.5 bg-red-100 rounded-sm mt-2.5">
						<p>Error: {error}</p>
					</div>
				)}
			</div>

			{validationResult && (
				<div className="validation-results">
					<h3>Validation Results</h3>
					{validationResult.is_valid ? (
						<p className="valid">Pipeline is valid! ✅</p>
					) : (
						<div className="invalid">
							<p>Pipeline is invalid! ❌</p>
							<ul className="error-list">
								{validationResult.errors.map((error, index) => (
									<li key={index}>{error}</li>
								))}
							</ul>
						</div>
					)}
				</div>
			)} */}

			{props.showStructure && pipelineStructure && (
				<div className="structure-summary">
					<h3>Pipeline Structure</h3>
					<p>Nodes: {pipelineStructure.nodes.length}</p>
					<p>Edges: {pipelineStructure.edges.length}</p>
				</div>
			)}
		</div>
	);
}
