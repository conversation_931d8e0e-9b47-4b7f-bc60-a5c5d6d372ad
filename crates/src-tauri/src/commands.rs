use std::path::PathBuf;
use std::fs;
use tauri::AppHandle;
use tauri::Manager;

use crate::pipeline::{Pipeline, ValidationResult, PipelineStructure};
use crate::schema_validator;

// Command to validate a pipeline JSON file
#[tauri::command]
pub fn validate_pipeline_file(file_path: &str, app_handle: AppHandle) -> Result<ValidationResult, String> {
    let path = PathBuf::from(file_path);

    // Get the schema path
    let mut resource_dir = app_handle.path().resource_dir()
        .map_err(|e| format!("Failed to get resource directory: {}", e))?;
    let schema_path = {
        resource_dir.push("schema/schema.json");
        if resource_dir.exists() {
            resource_dir
        } else {
            // Fallback to the bundled schema
            PathBuf::from("crates/src-tauri/schema/schema.json")
        }
    };

    if !schema_path.exists() {
        return Err("Could not find schema file".to_string());
    }

    // Validate the file
    schema_validator::validate_file(&path, &schema_path)
        .map_err(|err| format!("Validation error: {}", err))
}

// Command to validate a pipeline JSON string
#[tauri::command]
pub fn validate_pipeline_json(json_str: &str, app_handle: AppHandle) -> Result<ValidationResult, String> {
    // Get the schema path
    let mut resource_dir = app_handle.path().resource_dir()
        .map_err(|e| format!("Failed to get resource directory: {}", e))?;

    println!("resorce: {}", resource_dir.display());
    let schema_path = {
        resource_dir.push("schema/schema.json");
        if resource_dir.exists() {
            resource_dir
        } else {
            // Fallback to the bundled schema
            // PathBuf::from("src-tauri/schema/schema.json")
            let mut sc = app_handle.path().app_data_dir().map_err(|e| format!("Failed to get resource directory: {}", e))?;
            sc.push("schema.json");
            sc
        }
    };

    println!("{} and exist {}", schema_path.display(), app_handle.path().app_data_dir().unwrap().display());

    if !schema_path.exists() {
        return Err("Could not find schema file".to_string());
    }

    // Load the schema
    let schema = schema_validator::load_schema(&schema_path)
        .map_err(|err| format!("Error loading schema: {}", err))?;

    // Validate the JSON
    Ok(schema_validator::validate_json(json_str, &schema))
}

// Command to build a pipeline structure from a JSON file
#[tauri::command]
pub fn build_pipeline_structure(file_path: &str) -> Result<PipelineStructure, String> {
    // Load the pipeline from the file
    let pipeline = Pipeline::from_file(file_path)
        .map_err(|err| format!("Error loading pipeline: {}", err))?;

    // Build the structure
    pipeline.build_structure()
        .map_err(|err| format!("Error building pipeline structure: {}", err))
}

// Command to build a pipeline structure from a JSON string
#[tauri::command]
pub fn build_pipeline_structure_from_json(json_str: &str) -> Result<PipelineStructure, String> {
    // Parse the JSON into a pipeline
    let pipeline = Pipeline::from_json(json_str)
        .map_err(|err| format!("Error parsing pipeline: {}", err))?;

    // Build the structure
    pipeline.build_structure()
        .map_err(|err| format!("Error building pipeline structure: {}", err))
}

// Command to save a pipeline to a file
#[tauri::command]
pub fn save_pipeline(pipeline_json: &str, file_path: &str) -> Result<(), String> {
    // Validate the JSON first
    let pipeline = Pipeline::from_json(pipeline_json)
        .map_err(|err| format!("Invalid pipeline JSON: {}", err))?;

    // Serialize the pipeline to JSON
    let json = serde_json::to_string_pretty(&pipeline)
        .map_err(|err| format!("Error serializing pipeline: {}", err))?;

    // Write to file
    fs::write(file_path, json)
        .map_err(|err| format!("Error writing file: {}", err))
}
